
import copy
import os
import pickle
import numpy as np
import torch
from tqdm import tqdm
import torch.nn as nn
import shutil
import torch.nn.functional as F
from torchvision import datasets, transforms
from torch.utils.data import DataLoader
import torch.optim as optim
from matplotlib import pyplot as plt
import concurrent.futures
import re 
class ReducedLeNet5(nn.Module):
    def __init__(self):
        super(ReducedLeNet5, self).__init__()
        self.conv1 = nn.Conv2d(1, 3, kernel_size=5, stride=1)
        self.pool1 = nn.MaxPool2d(kernel_size=2, stride=2)
        self.conv2 = nn.Conv2d(3, 8, kernel_size=5, stride=1)
        self.pool2 = nn.MaxPool2d(kernel_size=2, stride=2)
        self.fc1 = nn.Linear(8 * 4 * 4, 64)
        self.fc2 = nn.Linear(64, 10)
    def forward(self, x):
        x = F.relu(self.conv1(x))
        x = self.pool1(x)
        x = F.relu(self.conv2(x))
        x = self.pool2(x)
        x = torch.flatten(x, 1)
        x = F.relu(self.fc1(x))
        x = self.fc2(x)
        return F.log_softmax(x, dim=1)
def clear_directory(save_dir):
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    else:
        for filename in os.listdir(save_dir):
            file_path = os.path.join(save_dir, filename)
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except (OSError, IOError) as e:
                print(f"Error deleting {file_path}: {e}")
def aggregate_models_dicts(cluster_models):
    # 检查输入列表是否为空
    if not cluster_models:
        raise ValueError("cluster_models 列表不能为空")
    # 通过复制第一个模型的参数字典来初始化结果
    # 使用 copy() 避免修改原始数据
    aggregated_params = copy.deepcopy(cluster_models[0])
    # 使用 torch.no_grad() 上下文管理器来避免计算梯度，节省内存
    with torch.no_grad():
        # 遍历模型参数字典中的每个键（如权重、偏置等）
        for key in aggregated_params:
            # 将所有模型的相同参数堆叠在一起，并计算平均值
            # 使用 float() 确保数值类型的一致性
            aggregated_params[key] = torch.mean(
                torch.stack([model[key].float() for model in cluster_models]), dim=0  # 在第0维度上计算平均值
            )
    return aggregated_params
def aggregate_nearest_neighbors_models(cluster_model_dict, nearest_neighbors):
    """修改后的聚合函数，考虑故障节点"""
    cluster_model_dict=copy.deepcopy(cluster_model_dict)
    new_models_dict = {}
    # 从配置获取故障节点列表
    faulty_nodes = config.get("faulty_nodes", [])
    
    for node, neighbors in nearest_neighbors.items():
        if node in faulty_nodes:
            continue  # 跳过故障节点
        
        # 过滤故障邻居节点
        valid_neighbors = [n for n in neighbors if n not in faulty_nodes]
        neighbor_values = [cluster_model_dict[n] for n in valid_neighbors]
        neighbor_values.append(cluster_model_dict[node])
        
        if neighbor_values: #仅当存在有效模型时聚合
            global_model = aggregate_models_dicts(neighbor_values)
            new_models_dict[node] = global_model
        else:
            new_models_dict[node] = cluster_model_dict[node]  # 保留原模型
            
    # 保留故障节点原有模型不更新
    for node in faulty_nodes:
        if node in cluster_model_dict:
            new_models_dict[node] = cluster_model_dict[node]
    
    return new_models_dict


def get_datasets(data_root, dataset_name, normalize=True):
    # 获取归一化参数
    if normalize:
        if dataset_name == "MNIST":
            mean, std = (0.1307,), (0.3081,)
        elif dataset_name == "FashionMNIST":
            mean, std = (0.2860,), (0.3530,)
        elif dataset_name == "CIFAR10":
            mean, std = (0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)
        elif dataset_name == "CIFAR100":
            mean, std = (0.5071, 0.4865, 0.4409), (0.2673, 0.2564, 0.2762)
        else:
            raise ValueError(f"Unsupported dataset: {dataset_name}")
        transform = transforms.Compose([transforms.ToTensor(), transforms.Normalize(mean, std)])
    else:
        transform = transforms.Compose([transforms.ToTensor()])
    # 加载数据集
    dataset_class = getattr(datasets, dataset_name)
    train_dataset = dataset_class(root=data_root, train=True, download=True, transform=transform)
    test_dataset = dataset_class(root=data_root, train=False, download=True, transform=transform)
    return train_dataset, test_dataset
def load_user_data(data_root, index_dir, dataset_name="MNIST", normalize=True):
    # 获取数据集
    train_dataset, test_dataset = get_datasets(data_root, dataset_name, normalize=normalize)
    # 加载用户样本索引
    user_sample_indices = torch.load(os.path.join(index_dir, "user_sample_index.pth"))
    # 创建用户数据集
    user_datasets = []
    for user, indices in user_sample_indices.items():
        user_dataset = torch.utils.data.Subset(train_dataset, indices)
        user_datasets.append(user_dataset)
    return user_datasets, test_dataset
# 保持原有的数据加载函数
def load_datasets(config):
    data_root = config["data_root"]
    index_dir = config["index_dir"]
    dataset_name = config["dataset_name"]
    batch_size = config["batch_size"]
    user_datasets, test_dataset = load_user_data(data_root, index_dir, dataset_name=dataset_name, normalize=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    # 为每个用户创建数据加载器
    train_dataloaders = []
    for user_dataset in user_datasets:
        user_dataloader = DataLoader(user_dataset, batch_size=batch_size, shuffle=True)
        train_dataloaders.append(user_dataloader)
    return train_dataloaders, test_loader
class Federation:
    """联合训练类，包含服务器和客户端的逻辑"""
    def __init__(self, current_user_model_dict,model, model_dict, train_dataloaders, num_users, device):
        self.current_user_model_dict=copy.deepcopy(current_user_model_dict)
        self.model = copy.deepcopy(model)
        self.global_model_dict = copy.deepcopy(model_dict)
        self.device = device
        self.num_users = num_users
        self.train_dataloaders = train_dataloaders
        self.clients = []
        self.initialize()
        self.new_user_model_dict=[]
    # 初始化,创建每个客户端并分配数据
    def initialize(self):
        """初始化客户端模型"""
        for user_idx in range(self.num_users):
            client_model = copy.deepcopy(self.model).to(self.device)
            client_model.load_state_dict(self.current_user_model_dict[user_idx])  # 此处加载训练模型
            client = {
                "model": client_model,
                "train_loader": self.train_dataloaders[user_idx],
                "optimizer": optim.Adam(client_model.parameters(), lr=0.001),
                "criterion": nn.CrossEntropyLoss(),
            }
            self.clients.append(client)
    def train_client(self, client, num_iterations):
        """训练单个客户端模型"""
        client["model"].train()
        data_iter = iter(client["train_loader"])
        for iteration in range(num_iterations):
            try:
                data, target = next(data_iter)
            except StopIteration:
                data_iter = iter(client["train_loader"])
                data, target = next(data_iter)
            data, target = data.to(self.device), target.to(self.device)
            client["optimizer"].zero_grad()
            output = client["model"](data)
            loss = client["criterion"](output, target)
            loss.backward()
            client["optimizer"].step()
            # print(f"[INFO] Client Iteration {iteration + 1}: loss = {loss.item():.4f}")
        # 输出模型字典
        return client["model"].state_dict()
    def aggregate_parameters(self, model_dicts):
        """聚合客户端模型的参数"""
        with torch.no_grad():
            aggregated_params = copy.deepcopy(model_dicts[0])
            for key in aggregated_params:
                aggregated_params[key] = torch.mean(torch.stack([modict[key].float() for modict in model_dicts]), dim=0)
        return aggregated_params
    def train(self, num_rounds, local_epochs, save_dir, name=""):
        """执行联邦学习训练过程"""
        # 确保保存目录存在
        os.makedirs(save_dir, exist_ok=True)
        for round_num in range(num_rounds):
            # print(f"\n[INFO] Global Round {round_num + 1}/{num_rounds}")
            client_model_dicts = []
            # 每个客户端执行本地训练
            for client in self.clients:
                client_model_dicts.append(self.train_client(client, local_epochs))

            self.new_user_model_dict=copy.deepcopy(client_model_dicts)
            # 聚合客户端模型参数
            aggregated_params = self.aggregate_parameters(client_model_dicts)
            # 全局模型更新
            self.global_model_dict = aggregated_params
            # 保存全局模型参数
            if num_rounds < 2:
                save_path = os.path.join(save_dir, f"model_round_{name}.pth")
            else:
                save_path = os.path.join(save_dir, f"model_round_{round_num + 1}.pth")
            torch.save(aggregated_params, save_path)
    # 此函数针对异步聚合设计
    def train_cluster(self, num_rounds, local_epochs, save_dir, name=""):
        """执行联邦学习训练过程"""
        # 确保保存目录存在
        for round_num in range(num_rounds):
            # print(f"\n[INFO] Global Round {round_num + 1}/{num_rounds}")
            client_model_dicts = []
            # 每个客户端执行本地训练
            for client in self.clients:
                client_model_dicts.append(self.train_client(client, local_epochs))
            
            self.new_user_model_dict=copy.deepcopy(client_model_dicts)
            # 聚合客户端模型参数
            aggregated_params = self.aggregate_parameters(client_model_dicts)
            # 全局模型更新
            self.global_model_dict = aggregated_params
    def get_parameters(self):
        """获取模型参数"""
        return self.global_model_dict
def aggregate_cluster_models(cluster_models):
    """
    聚合所有簇的全局模型
    """
    if not cluster_models:
        raise ValueError("cluster_models 列表不能为空")
    # 初始化聚合参数
    aggregated_params = copy.deepcopy(cluster_models[0])  # 避免修改原始数据
    # 使用 torch.no_grad() 确保不计算梯度
    with torch.no_grad():
        for key in aggregated_params:
            # 计算平均值
            aggregated_params[key] = torch.mean(torch.stack([model[key].float() for model in cluster_models]), dim=0)
    return aggregated_params


def train_clusters(config, clusters, train_dataloaders):
    if len(clusters) < config["num_rounds"]:
        raise ValueError("clusters 数量小于全局次数！")
    
    # path = "/home/<USER>/cl/Data/MNIST/cluster_data/all_neighbors_nodes.pkl"
    path=config["all_nodes_path"]
    with open(path, "rb") as file:
        all_neighbors_nodes = pickle.load(file)
    
    model_dict = {}
    for key in all_neighbors_nodes[0].keys():
        model_dict[key] = copy.deepcopy(config["model"].state_dict())
    
    # 初始化故障节点模型保留
    faulty_nodes = config.get("faulty_nodes", [])
    node_state=[]
    for node in model_dict:
        if node in faulty_nodes:
            node_state.append(True)
        else:
            node_state.append(False)          

    # for node in faulty_nodes:
    #     if node in model_dict:
    #         model_dict[node] = copy.deepcopy(config["model"].state_dict())

    all_user_model_dict=[copy.deepcopy(config["model"].state_dict()) for _ in range(config["num_users"])]

    for round_num in range(config["num_rounds"]):
        print(f'\n[INFO] Global Round {round_num + 1}/{config["num_rounds"]}')
        cluster_models = {}
        
        for cluster_id, cluster_users in enumerate(tqdm(clusters[round_num], colour="blue")):
            key = f"U{cluster_id+1}"
        
            #当前簇用户的模型
            current_user_model_dict = [all_user_model_dict[user_idx] for user_idx in cluster_users]
            #当前簇用户的训练数据
            current_cluster_dataloaders = [train_dataloaders[user_idx] for user_idx in cluster_users]
            current_cluster_num = len(cluster_users)
            
            cluser_federation = Federation(current_user_model_dict,
                config["model"], model_dict[key], current_cluster_dataloaders, current_cluster_num, config["device"])

            cluser_federation.train_cluster(1, config["local_epochs"], os.path.join(config["save_dir"], f"cluster_{cluster_id}"), f"{round_num}")

            cluster_model_dict = cluser_federation.get_parameters()
            cluster_models[key] = copy.deepcopy(cluster_model_dict)

            if node_state[cluster_id]:  # 故障节点的时候，本地模型不聚合   
                new_user_model_dict = cluser_federation.new_user_model_dict  # 获取新的用户模型字典列表
                for user_idx, user_model_dict in zip(cluster_users, new_user_model_dict):
                    all_user_model_dict[user_idx] = copy.deepcopy(user_model_dict)
        
        new_models_dict = aggregate_nearest_neighbors_models(cluster_models, all_neighbors_nodes[round_num])

        for i, (cluster_users, state) in enumerate(zip(clusters[round_num], node_state)):
            if not state:
                for user_idx in cluster_users:
                    all_user_model_dict[user_idx] = copy.deepcopy(new_models_dict[f"U{i+1}"])

        model_dict.update(new_models_dict)

        for key, value in model_dict.items():
            save_path = os.path.join(config["save_dir"], f"cluster_{key}")
            os.makedirs(save_path, exist_ok=True)
            if key in faulty_nodes:
                print(f"[WARN] Skipping save for faulty node {key}")
                continue

            save_global_path = os.path.join(save_path, f"model_round_{round_num}.pth")
            torch.save(value, save_global_path)

def cluster_split(config):
    # path = "/home/<USER>/cl/Data/MNIST/cluster_data/cluster_user.pkl"
    path = config["cluster_user_path"]
    # 打开 .pkl 文件
    with open(path, "rb") as file:
        # 从文件中加载对象
        clus_data = pickle.load(file)
    clusters = []
    for clu in clus_data:
        single_clu = []
        for key, value in clu.items():
            single_clu.append(value)
        clusters.append(single_clu)
    return clusters

def evaluate_model(model, test_loader, device):
    """评估模型"""
    model.eval()
    correct = 0
    total = 0    
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            _, predicted = torch.max(output.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
    
    accuracy = 100 * correct / total
    return accuracy

def evaluate_models_multithreaded(config, model_dir, test_loader, num_threads=4):
    # 新增故障节点过滤逻辑
    node_id = os.path.basename(model_dir).split("_")[-1]  # 从路径提取节点ID (如U1)
    if node_id in config.get("faulty_nodes", []):
        print(f"[WARN] Skipping evaluation for faulty node {node_id}")
        return []
    
    test_model=config["model"]
    device=config["device"]

    """多线程评估所有保存的模型"""
    model_files = [f for f in os.listdir(model_dir) if re.match(r"model_round_\d+\.pth", f)]
    model_files.sort(key=lambda x: int(re.search(r"model_round_(\d+)\.pth", x).group(1)))

    def evaluate_single_model(filename):
        model_path = os.path.join(model_dir, filename)
        model = copy.deepcopy(test_model).to(device)
        model.load_state_dict(torch.load(model_path))
        accuracy = evaluate_model(model, test_loader, device)
        return filename, accuracy

    accuracies = {}
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        future_to_model = {executor.submit(evaluate_single_model, filename): filename for filename in model_files}
        for future in concurrent.futures.as_completed(future_to_model):
            filename, accuracy = future.result()
            accuracies[filename] = accuracy
            print(f"[INFO] Model {filename} accuracy: {accuracy:.2f}%")


    with open(os.path.join(config["save_dir"], f"{node_id}_accuracies.pkl"), "wb") as f:
        pickle.dump(accuracies, f)

    return accuracies


def visualize_results(accuracies_dict, config,name=''):
    """可视化训练结果"""
    plt.figure(figsize=(10, 6))

    rounds = np.array([i for i in range(len(accuracies_dict))])
    accuracies = [accuracies_dict[f'model_round_{r}.pth'] for r in rounds]

    plt.plot(rounds + 1, accuracies, marker="o")
    plt.title(f"{name}_Global Model Accuracy Over Rounds")
    plt.xlabel("Round")
    plt.ylabel("Accuracy (%)")
    plt.grid(True)
    plt.savefig(os.path.join(config["save_dir"], f"{name}_accuracy_plot.png"))
    plt.close()

def visualize_all_clusters_accuracy(cluster_accuracies, config):
    """可视化所有簇的精度对比图"""
    plt.figure(figsize=(12, 8))
    
    # 子图1：各簇精度对比
    all_accuracies=[]
    plt.subplot(2, 1, 1)
    for cluster_id, accuracies_dict in cluster_accuracies.items():
        rounds = np.array([i for i in range(len(accuracies_dict))])
        accuracy_values = [accuracies_dict[f'model_round_{r}.pth'] for r in rounds]
        all_accuracies.append(accuracy_values)
        plt.plot(rounds + 1, accuracy_values, marker="o", label=f"Cluster {cluster_id}")
    
    plt.title("Accuracy Comparison Across Clusters")
    plt.xlabel("Round")
    plt.ylabel("Accuracy (%)")
    plt.legend()
    plt.grid(True)
    
    # 子图2：所有簇的平均精度
    plt.subplot(2, 1, 2)
    # rounds = np.array([i for i in range(len(next(iter(cluster_accuracies.values()))))])
    valid_accuracies = [acc for acc in all_accuracies if len(acc) > 0]
    mean_accuracies = np.mean(np.array(valid_accuracies), axis=0)
    plt.plot(rounds + 1, mean_accuracies, marker="o", color="red", label="Mean Accuracy")
    
    plt.title("Mean Accuracy Across Clusters")
    plt.xlabel("Round")
    plt.ylabel("Accuracy (%)")
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(config["save_dir"], "all_clusters_accuracy_comparison.png"))
    plt.close()

def main(config):    
    # 加载数据集
    train_dataloaders, test_loader = load_datasets(config)
    # 将用户分簇
    clusters = cluster_split(config)
    # 对每个簇进行联邦学习训练
    train_clusters(config, clusters, train_dataloaders)


if __name__ == "__main__":
    # 配置参数
    config = {
        "device": torch.device("cuda" if torch.cuda.is_available() else "cpu"),
        "data_root": "/home/<USER>/cl/Data/MNIST/data",
        "index_dir": "/home/<USER>/cl/Data/MNIST/index",
        "save_dir": "/home/<USER>/cl/Data/MNIST/result_save",
        "index_dir": "/home/<USER>/cl/Data/MNIST/index",
        "save_dir": "/home/<USER>/cl/Data/MNIST/result_save_60",
        "all_nodes_path": "/home/<USER>/cl/Data/MNIST/cluster_data/all_neighbors_nodes.pkl",
        "cluster_user_path": "/home/<USER>/cl/Data/MNIST/cluster_data/cluster_user.pkl",
        "dataset_name": "MNIST",  # 数据集名称,如MNIST、CIFAR10、CIFAR100、FashionMNIST
        "num_users": 150,
        "num_rounds": 200,
        "local_epochs": 10,
        "batch_size": 8,
    }

    config["data_root"]="F:/实验数据/20250113/data"
    config["index_dir"]='F:/实验数据/Month_7/MainData/数据集分布/normal/index'
    config["save_dir"]= 'F:/实验数据/Month_7/台式机数据/实验数据/FL/EP200-IID'
    config["model"] = ReducedLeNet5()
    config["index_dir"] = os.path.join(config["index_dir"], config["dataset_name"])
    # config["save_dir"] = os.path.join(config["save_dir"], "model_save/" + config["dataset_name"])
    config["all_nodes_path"]='F:/实验数据/Month_7/台式机数据/实验数据/7/note=2/all_neighbors_nodes.pkl'
    config["cluster_user_path"]='F:/实验数据/20250113/cluster_data/cluster_user.pkl'

    faulty_nodes_percentages = {
        # 0: [],
        # 10: ["U9"],
        # 30: ["U9", "U10", "U3"],
        # 50: ["U9", "U10", "U3", "U7", "U2"],
        70: ["U9", "U10", "U3", "U7", "U2", "U6", "U1"],
        90: ["U9", "U10", "U3", "U7", "U2", "U6", "U1", "U8", "U5"]
    }

    model_test=False#模型训练
    model_test=True#模型评估
    save_dir=config["save_dir"]

    for percentage, faulty_nodes in faulty_nodes_percentages.items():
        config["faulty_nodes"] = faulty_nodes
        config["save_dir"]=save_dir
        
        config["save_dir"]=config["save_dir"]+f'/MNIST_{percentage}'

        if not model_test:
            main(config)
        else:           
            _, test_loader = load_datasets(config)   
            cluster_dirs = [os.path.join(config["save_dir"], d) for d in os.listdir(config["save_dir"]) if d.startswith('cluster_')]
            cluster_accuracies = {}
            for i, cluster in enumerate(cluster_dirs):          
                print(f"\n[INFO] Evaluating models in {cluster}")
                # 评估结果
                accuracies = evaluate_models_multithreaded(config, cluster, test_loader, num_threads=1)
                cluster_accuracies[i + 1] = accuracies
                # with open(os.path.join(config["save_dir"], f"{i+1}_accuracies.pkl"), "wb") as f:
                #     pickle.dump(accuracies, f)
                # 可视化结果
                # visualize_results(accuracies, config,name=f'{i+1}_cluster')
            # 可视化所有簇的精度对比图
            # visualize_all_clusters_accuracy(cluster_accuracies, config)
