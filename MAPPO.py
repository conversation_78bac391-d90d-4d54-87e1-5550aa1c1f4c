# 2025年03月19日 21:04:53 配合MAPPO，用于对比实验，目前还没有验证

import torch
import torch.nn as nn

class CTDEActorCriticNetwork(nn.Module):
    def __init__(self, agent_num: int, local_obs_shape: int, global_obs_shape: int, discrete_action_shape: int, continuous_action_dim: int) -> None:
        super(CTDEActorCriticNetwork, self).__init__()
        self.agent_num = agent_num
        
        # 局部观察编码器
        self.local_encoder = nn.Sequential(
            nn.Linear(local_obs_shape, 64),
            nn.ReLU(),
            nn.Linear(64, 128),
            nn.ReLU(),
        )
        
        # 全局观察编码器
        self.global_encoder = nn.Sequential(
            nn.Linear(global_obs_shape, 64),
            nn.ReLU(),
            nn.Linear(64, 128),
            nn.ReLU(),
        )
        
        # 策略网络：离散动作（信道选择）
        self.policy_head_discrete = nn.Linear(128, discrete_action_shape)  # 输出 30 个信道的 logits
        
        # 策略网络：连续动作（位置调整）
        self.policy_head_continuous_mu = nn.Linear(128, continuous_action_dim)  # 输出均值
        self.policy_head_continuous_sigma = nn.Linear(128, continuous_action_dim)  # 输出对数方差
        
        # 价值网络
        self.value_head = nn.Linear(128, 1)

    def forward(self, local_obs: torch.Tensor, global_obs: torch.Tensor):
        # local_obs: (batch_size, agent_num, local_obs_shape)
        # global_obs: (batch_size, agent_num, global_obs_shape)
        
        x_local = self.local_encoder(local_obs)  # (batch_size, agent_num, 128)
        x_global = self.global_encoder(global_obs)  # (batch_size, agent_num, 128)
        
        # 离散动作 logits
        logit_discrete = self.policy_head_discrete(x_local)  # (batch_size, agent_num, discrete_action_shape)
        
        # 连续动作均值和方差
        mu_continuous = self.policy_head_continuous_mu(x_local)  # (batch_size, agent_num, continuous_action_dim)
        sigma_continuous = torch.exp(self.policy_head_continuous_sigma(x_local))  # (batch_size, agent_num, continuous_action_dim)
        
        # 价值估计
        value = self.value_head(x_global)  # (batch_size, agent_num, 1)
        
        return {
            'logit_discrete': logit_discrete,
            'mu_continuous': mu_continuous,
            'sigma_continuous': sigma_continuous,
            'value': value
        }

# 示例初始化
agent_num = 10  # 10 架无人机
local_obs_shape = 6  # 局部观察维度
global_obs_shape = 50  # 全局观察维度
discrete_action_shape = 30  # 30 个信道
continuous_action_dim = 2  # Δx, Δy
network = CTDEActorCriticNetwork(agent_num, local_obs_shape, global_obs_shape, discrete_action_shape, continuous_action_dim)

import numpy as np
from sklearn.cluster import KMeans

def generate_data(batch_size, agent_num, local_obs_shape, global_obs_shape, num_users=150, num_channels=30):
    # 随机生成用户位置
    user_positions = np.random.uniform(0, 100, (batch_size, num_users, 2))  # (batch_size, 150, 2)
    
    # K-means 聚类
    drone_positions = np.zeros((batch_size, agent_num, 2))  # (batch_size, 10, 2)
    cluster_centers = np.zeros((batch_size, agent_num, 2))
    for b in range(batch_size):
        kmeans = KMeans(n_clusters=agent_num).fit(user_positions[b])
        drone_positions[b] = kmeans.cluster_centers_  # 初始无人机位置为簇中心
        cluster_centers[b] = kmeans.cluster_centers_
    
    # 随机信道分配
    downlink_channels = np.random.randint(0, num_channels, (batch_size, agent_num))  # (batch_size, 10)
    
    # 局部观察
    local_state = np.zeros((batch_size, agent_num, local_obs_shape))
    for b in range(batch_size):
        for a in range(agent_num):
            local_state[b, a] = np.concatenate([
                drone_positions[b, a],  # 自身位置 (2 维)
                cluster_centers[b, a],  # 簇中心 (2 维)
                [downlink_channels[b, a]],  # 下行信道 (1 维)
                [(user_positions[b] - cluster_centers[b, a]).sum() / num_users]  # 简化的簇内用户数信息 (1 维)
            ])
    
    # 全局观察
    global_state = np.zeros((batch_size, agent_num, global_obs_shape))
    for b in range(batch_size):
        global_info = np.concatenate([
            drone_positions[b].flatten(),  # 所有无人机位置 (20 维)
            cluster_centers[b].flatten(),  # 所有簇中心 (20 维)
            downlink_channels[b]  # 所有下行信道 (10 维)
        ])
        for a in range(agent_num):
            global_state[b, a] = global_info
    
    return torch.tensor(local_state, dtype=torch.float32), torch.tensor(global_state, dtype=torch.float32)

# 示例调用
batch_size = 4
local_state, global_state = generate_data(batch_size, agent_num, local_obs_shape, global_obs_shape)

def compute_reward(drone_positions, user_positions, downlink_channels, num_channels=30):
    # 简化假设：奖励为无人机与簇中心的平均距离倒数（实际需计算速率）
    batch_size, agent_num, _ = drone_positions.shape
    rewards = np.zeros((batch_size, agent_num))
    for b in range(batch_size):
        for a in range(agent_num):
            dist = np.linalg.norm(drone_positions[b, a] - user_positions[b, a * 15:(a + 1) * 15], axis=1).mean()  # 假设每簇 15 个用户
            rewards[b, a] = 1 / (dist + 1e-6)  # 距离越小，奖励越高
    return torch.tensor(rewards, dtype=torch.float32)

def mappo_training_opeator(network, local_state, global_state):
    # 前向传播
    output = network(local_state, global_state)
    
    # 离散动作采样
    logit_discrete = output['logit_discrete']
    dist_discrete = torch.distributions.Categorical(logits=logit_discrete)
    action_discrete = dist_discrete.sample()  # (batch_size, agent_num)
    
    # 连续动作采样
    mu_continuous = output['mu_continuous']
    sigma_continuous = output['sigma_continuous']
    dist_continuous = torch.distributions.Normal(mu_continuous, sigma_continuous)
    action_continuous = dist_continuous.sample()  # (batch_size, agent_num, 2)
    
    # 更新无人机位置
    drone_positions = local_state[:, :, :2] + action_continuous  # 更新位置
    
    # 计算奖励（示例）
    user_positions = np.random.uniform(0, 100, (batch_size, 150, 2))  # 模拟用户位置
    reward = compute_reward(drone_positions.detach().numpy(), user_positions, action_discrete.numpy())
    
    # 损失计算（简化示例）
    value = output['value'].squeeze(-1)  # (batch_size, agent_num)
    advantage = reward - value.detach()
    policy_loss = -(dist_discrete.log_prob(action_discrete) + dist_continuous.log_prob(action_continuous)).mean()
    value_loss = (reward - value).pow(2).mean()
    
    return policy_loss + value_loss

# 示例调用
loss = mappo_training_opeator(network, local_state, global_state)