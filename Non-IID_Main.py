import torch
import random
import numpy as np
import os
from datasets import get_datasets
from torch.utils.data import Dataset
from typing import Dict, List, Tuple, Any

class NonIIDDataDistributor:
    def __init__(
        self,
        config: Dict[str, Any],
        seed: int = 42,
        normalize: bool = True,
        max_samples_per_user: int = 10000,
        samples_for_label: int = 300
    ):
        """
        Initialize the Non-IID data distributor for federated learning.
        
        Args:
            config: Configuration dictionary containing parameters
            seed: Random seed for reproducibility
            normalize: Whether to normalize the dataset
            max_samples_per_user: Maximum samples per user
            samples_for_label: Number of samples to take per label
        """
        self.config = config
        self.seed = seed
        self.normalize = normalize
        self.max_samples_per_user = max_samples_per_user
        self.samples_for_label = samples_for_label
        
        # Set random seeds
        random.seed(self.seed)
        np.random.seed(self.seed)
        torch.manual_seed(self.seed)
        
        self.num_users = config["num_users"]
        self.num_clusters = config.get("num_clusters", 10)
        self.min_labels_per_user = config.get("min_labels_per_user", 2)
        self.max_labels_per_user = config.get("max_labels_per_user", 10)
        
        # Load dataset
        self.train_dataset, _ = get_datasets(
            self.config["data_root"], 
            self.config["dataset_name"], 
            normalize=self.normalize
        )
        
        # Extract labels
        self.labels = []
        for _, label in self.train_dataset:
            self.labels.append(label if isinstance(label, int) else label.item())
        
        self.label_counts = {label: self.labels.count(label) for label in set(self.labels)}
        self.num_labels = len(self.label_counts)
        self.max_labels_per_user = min(self.max_labels_per_user, self.num_labels)
        self.min_labels_per_user = min(self.min_labels_per_user, self.max_labels_per_user)
        
        # Create label to indices mapping
        self.label_to_indices = {}
        for idx, (_, lbl) in enumerate(self.train_dataset):
            lbl = lbl if isinstance(lbl, int) else lbl.item()
            if lbl not in self.label_to_indices:
                self.label_to_indices[lbl] = []
            self.label_to_indices[lbl].append(idx)

    
    def distribute_data(self, method: str = "label_skew") -> Tuple[Dict[int, List[int]], Dict[int, List[int]]]:
        """
        Distribute data to users based on selected method.
        
        Args:
            method: Distribution method ('iid', 'label_skew', 'quantity_skew', 'feature_skew', 'combined')
            
        Returns:
            Tuple of user_sample_indices and label_assignments
        """
        if method == "iid":
            return self._iid_distribution()
        elif method == "label_skew":
            return self._label_skew_distribution()
        elif method == "quantity_skew":
            return self._quantity_skew_distribution()
        elif method == "feature_skew":
            return self._feature_skew_distribution()
        elif method == "combined":
            return self._combined_distribution()
        else:
            raise ValueError(f"Unknown distribution method: {method}")


    def _iid_distribution(self) -> Tuple[Dict[int, List[int]], Dict[int, List[int]]]:
        """
        Create IID distribution where each user gets a random subset of samples
        with approximately equal distribution of labels.
        
        Returns:
            Tuple of user_sample_indices and label_assignments
        """
        all_indices = list(range(len(self.train_dataset)))
        random.shuffle(all_indices)
        
        # Calculate samples per user to distribute dataset evenly
        samples_per_user = min(
            len(all_indices) // self.num_users,
            self.max_samples_per_user
        )
        
        # Distribute indices to users
        user_sample_indices = {user: [] for user in range(self.num_users)}
        for user_idx in range(self.num_users):
            start_idx = user_idx * samples_per_user
            end_idx = start_idx + samples_per_user
            user_sample_indices[user_idx] = all_indices[start_idx:end_idx]
        
        # Track label assignments based on the samples
        label_assignments = {user: set() for user in range(self.num_users)}
        for user, indices in user_sample_indices.items():
            for idx in indices:
                label = self.labels[idx]
                label_assignments[user].add(label)
            # Convert sets to lists for consistency with other methods
            label_assignments[user] = list(label_assignments[user])
        
        return user_sample_indices, label_assignments

    def _label_skew_distribution(self) -> Tuple[Dict[int, List[int]], Dict[int, List[int]]]:
        """
        Create label-skewed distribution where each user gets a limited number of labels.
        
        Returns:
            Tuple of user_sample_indices and label_assignments
        """
        # Assign labels to users
        label_assignments = {}
        available_labels = list(self.label_counts.keys())
        
        for user in range(self.num_users):
            # Determine which cluster this user belongs to
            cluster_id = user // (self.num_users // self.num_clusters)
            
            # Bias label selection towards cluster-related labels
            cluster_primary_labels = [(cluster_id + i) % self.num_labels for i in range(3)]
            other_labels = [lbl for lbl in available_labels if lbl not in cluster_primary_labels]
            
            # Determine number of labels for this user
            num_assigned_labels = random.randint(self.min_labels_per_user, self.max_labels_per_user)
            
            # Select labels with bias towards cluster-primary labels
            primary_count = min(num_assigned_labels, len(cluster_primary_labels))
            assigned_labels = random.sample(cluster_primary_labels, primary_count)
            
            # Add more labels if needed
            if num_assigned_labels > primary_count:
                secondary_count = num_assigned_labels - primary_count
                assigned_labels.extend(random.sample(other_labels, secondary_count))
            
            label_assignments[user] = assigned_labels
        
        # Assign samples to users based on labels
        user_sample_indices = {user: [] for user in range(self.num_users)}
        
        for user, assigned_labels in label_assignments.items():
            for label in assigned_labels:
                label_indices = self.label_to_indices.get(label, [])
                random.shuffle(label_indices)
                indices_for_user = label_indices[:self.samples_for_label]
                user_sample_indices[user].extend(indices_for_user)
                
                # Ensure we don't exceed max samples per user
                if len(user_sample_indices[user]) > self.max_samples_per_user:
                    user_sample_indices[user] = user_sample_indices[user][:self.max_samples_per_user]
                    break
        
        return user_sample_indices, label_assignments
    
    def _quantity_skew_distribution(self) -> Tuple[Dict[int, List[int]], Dict[int, List[int]]]:
        """
        Create quantity-skewed distribution where users get different amounts of data.
        
        Returns:
            Tuple of user_sample_indices and label_assignments
        """
        # First create a basic label distribution
        user_sample_indices, label_assignments = self._label_skew_distribution()
        
        # Create power-law distribution for data quantity
        user_data_amounts = np.random.pareto(a=2, size=self.num_users)
        user_data_amounts = user_data_amounts / np.sum(user_data_amounts)
        
        # Scale to ensure we don't exceed max samples
        total_desired_samples = min(
            sum(len(indices) for indices in user_sample_indices.values()),
            self.num_users * self.max_samples_per_user
        )
        
        target_samples = [int(total_desired_samples * amount) for amount in user_data_amounts]
        
        # Adjust user data based on quantities
        for user_idx in range(self.num_users):
            current_samples = len(user_sample_indices[user_idx])
            target = min(target_samples[user_idx], self.max_samples_per_user)
            
            if current_samples > target:
                # Remove excess samples
                user_sample_indices[user_idx] = random.sample(
                    user_sample_indices[user_idx], target
                )
            elif current_samples < target:
                # Need more samples - try to add more from assigned labels
                needed = target - current_samples
                additional_samples = []
                
                for label in label_assignments[user_idx]:
                    if needed <= 0:
                        break
                        
                    # Get indices not yet assigned to this user
                    available_indices = [
                        idx for idx in self.label_to_indices[label]
                        if idx not in user_sample_indices[user_idx]
                    ]
                    
                    if available_indices:
                        add_count = min(needed, len(available_indices))
                        additional_samples.extend(random.sample(available_indices, add_count))
                        needed -= add_count
                
                user_sample_indices[user_idx].extend(additional_samples)
        
        return user_sample_indices, label_assignments
    
    def _feature_skew_distribution(self) -> Tuple[Dict[int, List[int]], Dict[int, List[int]]]:
        """
        Create feature-skewed distribution where different clusters get different feature transforms.
        Note: This method returns the same indices as label_skew but is meant to be used
        with transformed datasets.
        
        Returns:
            Tuple of user_sample_indices and label_assignments
        """
        # Use the same distribution as label skew
        user_sample_indices, label_assignments = self._label_skew_distribution()
        
        # Create cluster transformations
        cluster_transformations = []
        for cluster_id in range(self.num_clusters):
            # Different rotation angles
            rotation_angle = (cluster_id * 15) % 60  # Moderate rotations to maintain readability
            
            # Different noise levels
            noise_level = 0.01 + (cluster_id * 0.005)  # Subtle noise
            
            # Different contrast/brightness
            contrast = 0.9 + (cluster_id * 0.03)
            
            # Different translations
            translation_x = int((cluster_id % 3) * 2 - 2)  # -2, 0, or 2 pixels
            translation_y = int((cluster_id // 3 % 3) * 2 - 2)  # -2, 0, or 2 pixels
            
            cluster_transformations.append({
                'rotation': rotation_angle,
                'noise': noise_level,
                'contrast': contrast,
                'translation_x': translation_x,
                'translation_y': translation_y
            })
        
        # Store transformations in config for later use when loading data
        self.config["cluster_transformations"] = cluster_transformations
        
        return user_sample_indices, label_assignments
    
    def _combined_distribution(self) -> Tuple[Dict[int, List[int]], Dict[int, List[int]]]:
        """
        Create a combined distribution using all three methods.
        
        Returns:
            Tuple of user_sample_indices and label_assignments
        """
        # Start with label skew
        user_sample_indices, label_assignments = self._label_skew_distribution()
        
        # Apply quantity skew
        user_sample_indices, label_assignments = self._apply_quantity_skew(
            user_sample_indices, label_assignments
        )
        
        # For feature skew, we would apply transformations at dataset loading time
        # using the cluster assignments
        
        return user_sample_indices, label_assignments
    
    def _apply_quantity_skew(
        self, 
        user_sample_indices: Dict[int, List[int]], 
        label_assignments: Dict[int, List[int]]
    ) -> Tuple[Dict[int, List[int]], Dict[int, List[int]]]:
        """
        Apply quantity skew to an existing distribution.
        
        Args:
            user_sample_indices: Existing user sample indices
            label_assignments: Existing label assignments
            
        Returns:
            Tuple of updated user_sample_indices and label_assignments
        """
        # Create power-law distribution for data quantity
        alpha = 2.0  # Pareto distribution parameter
        user_data_amounts = np.random.pareto(a=alpha, size=self.num_users)
        
        # Make distribution more extreme
        user_data_amounts = user_data_amounts ** 1.5
        
        # Normalize to sum to 1
        user_data_amounts = user_data_amounts / np.sum(user_data_amounts)
        
        # Scale to ensure we don't exceed max samples
        total_samples = sum(len(indices) for indices in user_sample_indices.values())
        target_samples = [
            min(int(total_samples * amount), self.max_samples_per_user)
            for amount in user_data_amounts
        ]
        
        # Adjust user data based on quantities
        for user_idx in range(self.num_users):
            current_samples = len(user_sample_indices[user_idx])
            
            if current_samples > target_samples[user_idx]:
                # Remove excess samples
                user_sample_indices[user_idx] = random.sample(
                    user_sample_indices[user_idx], target_samples[user_idx]
                )
            elif current_samples < target_samples[user_idx]:
                # Try to add more samples from assigned labels
                needed = target_samples[user_idx] - current_samples
                additional_samples = []
                
                for label in label_assignments[user_idx]:
                    # Get indices not yet assigned to this user
                    available_indices = [
                        idx for idx in self.label_to_indices[label]
                        if idx not in user_sample_indices[user_idx]
                    ]
                    
                    if available_indices:
                        add_count = min(needed, len(available_indices))
                        additional_samples.extend(random.sample(available_indices, add_count))
                        needed -= add_count
                        
                        if needed <= 0:
                            break
                
                user_sample_indices[user_idx].extend(additional_samples)
        
        return user_sample_indices, label_assignments
    
    def create_transformed_datasets(self, user_sample_indices: Dict[int, List[int]]) -> Dict[int, Dataset]:
        """
        Create transformed datasets for each user based on their cluster.
        
        Args:
            user_sample_indices: Dictionary mapping users to their sample indices
            
        Returns:
            Dictionary mapping users to their transformed datasets
        """
        user_datasets = {}
        users_per_cluster = self.num_users // self.num_clusters
        
        # Create transformations for each cluster
        cluster_transformations = []
        for cluster_id in range(self.num_clusters):
            # Different rotation angles
            rotation_angle = (cluster_id * 15) % 60
            
            # Different noise levels
            noise_level = 0.01 + (cluster_id * 0.005)
            
            # Different contrast/brightness
            contrast = 0.9 + (cluster_id * 0.03)
            
            # Different translations
            translation_x = int((cluster_id % 3) * 2 - 2)
            translation_y = int((cluster_id // 3 % 3) * 2 - 2)
            
            cluster_transformations.append({
                'rotation': rotation_angle,
                'noise': noise_level,
                'contrast': contrast,
                'translation_x': translation_x,
                'translation_y': translation_y
            })
        
        # Create transformed dataset for each user
        for user_idx in range(self.num_users):
            cluster_id = user_idx // users_per_cluster
            transform_params = cluster_transformations[cluster_id]
            
            user_datasets[user_idx] = TransformedDataset(
                base_dataset=self.train_dataset,
                user_indices=user_sample_indices[user_idx],
                transform_params=transform_params
            )
        
        return user_datasets
    
    def save_distribution(self, user_sample_indices: Dict[int, List[int]], 
                         label_assignments: Dict[int, List[int]]) -> None:
        """
        Save the distributed data to files.
        
        Args:
            user_sample_indices: Dictionary mapping users to their sample indices
            label_assignments: Dictionary mapping users to their assigned labels
        """
        save_dir = self.config["index_dir"]
        os.makedirs(save_dir, exist_ok=True)
        
        torch.save(user_sample_indices, os.path.join(save_dir, "user_sample_index.pth"))
        torch.save(label_assignments, os.path.join(save_dir, "label_assignments.pth"))
        
        print(f"Data saved to {save_dir}")
        
        # Print some statistics
        users_per_cluster = self.num_users // self.num_clusters
        
        for cluster_id in range(self.num_clusters):
            cluster_users = range(cluster_id * users_per_cluster, 
                                 (cluster_id + 1) * users_per_cluster)
            cluster_sample_count = sum(len(user_sample_indices[user]) for user in cluster_users)
            cluster_label_count = sum(len(label_assignments[user]) for user in cluster_users)
            
            print(f"Cluster {cluster_id}: {cluster_sample_count} samples, " 
                  f"{cluster_label_count} labels total")
            
        # Print total statistics
        total_samples = sum(len(indices) for indices in user_sample_indices.values())
        avg_samples = total_samples / self.num_users
        min_samples = min(len(indices) for indices in user_sample_indices.values())
        max_samples = max(len(indices) for indices in user_sample_indices.values())
        
        print(f"Total samples: {total_samples}")
        print(f"Average samples per user: {avg_samples:.2f}")
        print(f"Min samples per user: {min_samples}")
        print(f"Max samples per user: {max_samples}")

def distribute_data(
    config: Dict[str, Any],
    method: str = "combined",
    seed: int = 42,
    normalize: bool = True,
    max_samples_per_user: int = 10000,
    samples_for_label: int = 300
) -> None:
    """
    Wrapper function to distribute data and save the results.
    
    Args:
        config: Configuration dictionary
        method: Distribution method ('iid', 'label_skew', 'quantity_skew', 'feature_skew', 'combined')
        seed: Random seed
        normalize: Whether to normalize the dataset
        max_samples_per_user: Maximum samples per user
        samples_for_label: Number of samples to take per label
    """
    print(f"Creating data distribution using method: {method}")
    
    distributor = NonIIDDataDistributor(
        config=config,
        seed=seed,
        normalize=normalize,
        max_samples_per_user=max_samples_per_user,
        samples_for_label=samples_for_label
    )
    
    # Create distribution
    user_sample_indices, label_assignments = distributor.distribute_data(method=method)
    
    # Save distribution
    distributor.save_distribution(user_sample_indices, label_assignments)
    
    # Print method-specific information
    if method == "iid":
        print("\nIID Distribution Statistics:")
        label_counts_per_user = {}
        for user in range(config["num_users"]):
            label_counts = {}
            for idx in user_sample_indices[user]:
                label = distributor.labels[idx]
                label_counts[label] = label_counts.get(label, 0) + 1
            label_counts_per_user[user] = label_counts
        
        # Print label distribution for a few sample users
        sample_users = min(5, config["num_users"])
        for user in range(sample_users):
            print(f"  User {user} label distribution: {label_counts_per_user[user]}")
        
        # Calculate and print label distribution uniformity
        label_distribution_variance = []
        for label in range(distributor.num_labels):
            user_ratios = []
            for user in range(config["num_users"]):
                user_count = len(user_sample_indices[user])
                label_count = label_counts_per_user[user].get(label, 0)
                if user_count > 0:
                    user_ratios.append(label_count / user_count)
            if user_ratios:
                variance = np.var(user_ratios)
                label_distribution_variance.append(variance)
        
        avg_variance = np.mean(label_distribution_variance)
        print(f"  Average variance in label distribution across users: {avg_variance:.6f}")
        print(f"  (Lower variance indicates more uniform IID distribution)")
    
    elif method == "label_skew" or method == "combined":
        print("\nLabel Distribution:")
        all_labels = set(range(distributor.num_labels))  # For any dataset
        for cluster_id in range(config["num_clusters"]):
            users_per_cluster = config["num_users"] // config["num_clusters"]
            start_user = cluster_id * users_per_cluster
            end_user = (cluster_id + 1) * users_per_cluster
            
            cluster_labels = set()
            for user in range(start_user, end_user):
                cluster_labels.update(label_assignments[user])
            
            missing_labels = all_labels - cluster_labels
            print(f"  Cluster {cluster_id}: Labels present: {sorted(cluster_labels)}, "
                  f"Missing labels: {sorted(missing_labels)}")
    
    if method == "quantity_skew" or method == "combined":
        print("\nQuantity Distribution:")
        sample_counts = [len(user_sample_indices[user]) for user in range(config["num_users"])]
        print(f"  Min samples: {min(sample_counts)}")
        print(f"  Max samples: {max(sample_counts)}")
        print(f"  Mean samples: {sum(sample_counts)/len(sample_counts):.2f}")
        print(f"  Sample variance: {np.var(sample_counts):.2f}")
    
    if method == "feature_skew" or method == "combined":
        print("\nFeature Transformations:")
        users_per_cluster = config["num_users"] // config["num_clusters"]
        for cluster_id in range(config["num_clusters"]):
            if "cluster_transformations" in distributor.config:
                params = distributor.config["cluster_transformations"][cluster_id]
                print(f"  Cluster {cluster_id} (Users {cluster_id*users_per_cluster}-"
                      f"{(cluster_id+1)*users_per_cluster-1}):")
                for param, value in params.items():
                    print(f"    {param}: {value}")

if __name__ == "__main__":
    config = {
        "device": torch.device("cuda" if torch.cuda.is_available() else "cpu"),
        "data_root":"F:/实验数据/20250113/data",
        "index_dir": "F:/实验数据/Month_7/MainData/数据集分布/normal/index",#划分好的数据集索引存放位置
        "save_dir": "F:/实验数据/20250113/index",
        "dataset_name": "MNIST",
        "num_users": 150,
        "num_clusters": 10,
        "min_labels_per_user": 1,
        "max_labels_per_user": 3  # Limiting to fewer labels per user for more heterogeneity
    }
    
    config["index_dir"] = os.path.join(config["index_dir"], config["dataset_name"])
    config["save_dir"] = os.path.join(config["save_dir"], config["dataset_name"])
    
    # You can choose any of these methods: "label_skew", "quantity_skew", "feature_skew", "combined","iid"
    distribute_data(
        config=config, 
        method="iid",  # Use combined method for maximum heterogeneity
        seed=26, 
        normalize=True, 
        max_samples_per_user=10000, 
        samples_for_label=300
    )
