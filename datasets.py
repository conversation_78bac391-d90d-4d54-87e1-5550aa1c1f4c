#这里存放需要处理的数据集
import os
import torch
from torchvision import datasets, transforms


def get_datasets(data_root, dataset_name, normalize=True):
    """
    获取训练集和测试集，并根据normalize参数决定是否进行归一化。
    如果normalize为True，则根据数据集名称返回对应的归一化参数进行处理。

    参数:
    data_root (str): 数据集的存储路径
    dataset_name (str): 数据集名称，支持 'MNIST', 'FashionMNIST', 'CIFAR10', 'CIFAR100'
    normalize (bool): 是否进行归一化
    
    返回:
    train_dataset: 训练数据集
    test_dataset: 测试数据集
    """
    # 获取归一化参数
    if normalize:
        if dataset_name == 'MNIST':
            mean, std = (0.1307,), (0.3081,)
        elif dataset_name == 'FashionMNIST':
            mean, std = (0.2860,), (0.3530,)
        elif dataset_name == 'CIFAR10':
            mean, std = (0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)
        elif dataset_name == 'CIFAR100':
            mean, std = (0.5071, 0.4865, 0.4409), (0.2673, 0.2564, 0.2762)
        else:
            raise ValueError(f"Unsupported dataset: {dataset_name}")
        
        transform = transforms.Compose([transforms.ToTensor(), transforms.Normalize(mean, std)])
    else:
        transform = transforms.Compose([transforms.ToTensor()])
    
    # 加载数据集
    dataset_class = getattr(datasets, dataset_name)
    train_dataset = dataset_class(root=data_root, train=True, download=True, transform=transform)
    test_dataset = dataset_class(root=data_root, train=False, download=True, transform=transform)
    
    return train_dataset, test_dataset


def load_user_data(data_root, index_dir, dataset_name='MNIST', normalize=True):
    """
    从索引文件中加载用户数据集。
    user_datasets: 一个包含所有用户数据集的列表。
    """
    # 获取数据集
    train_dataset, test_dataset = get_datasets(data_root, dataset_name, normalize=normalize)

    # 加载用户样本索引
    user_sample_indices = torch.load(os.path.join(index_dir, 'user_sample_index.pth'))

    # 创建用户数据集
    user_datasets = []
    for user, indices in user_sample_indices.items():
        user_dataset = torch.utils.data.Subset(train_dataset, indices)
        user_datasets.append(user_dataset)

    # # 为每个用户创建数据加载器
    # user_dataloaders = []
    # for user_dataset in user_datasets:
    #     user_dataloader = DataLoader(user_dataset, batch_size=32, shuffle=True, num_workers=4)
    #     user_dataloaders.append(user_dataloader)

    return user_datasets,test_dataset
